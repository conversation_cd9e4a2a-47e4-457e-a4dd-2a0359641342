part of services;

class DatabaseService extends GetxService {
  static DatabaseService get to => Get.find();
  
  late Database _database;
  Database get database => _database;
  
  static const String _databaseName = 'cheestack.db';
  static const int _databaseVersion = 1;

  Future<DatabaseService> init() async {
    await _initDatabase();
    return this;
  }

  Future<void> _initDatabase() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, _databaseName);
    
    _database = await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // 创建用户表
    await db.execute('''
      CREATE TABLE users (
        id TEXT PRIMARY KEY,
        username TEXT,
        mobile TEXT,
        email TEXT,
        avatar TEXT,
        intro TEXT,
        is_superuser INTEGER DEFAULT 0,
        status INTEGER DEFAULT 1,
        created_at TEXT,
        updated_at TEXT,
        synced_at TEXT,
        is_dirty INTEGER DEFAULT 0
      )
    ''');

    // 创建用户配置表
    await db.execute('''
      CREATE TABLE user_configs (
        id INTEGER PRIMARY KEY,
        user_id TEXT,
        is_auto_play_audio INTEGER DEFAULT 0,
        is_auto_play_ai_audio INTEGER DEFAULT 0,
        review_number INTEGER DEFAULT 20,
        study_number INTEGER DEFAULT 20,
        study_type INTEGER DEFAULT 1,
        current_study_id INTEGER,
        editing_book_id INTEGER,
        created_at TEXT,
        updated_at TEXT,
        synced_at TEXT,
        is_dirty INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // 创建书籍表
    await db.execute('''
      CREATE TABLE books (
        id INTEGER PRIMARY KEY,
        user_id TEXT,
        name TEXT NOT NULL,
        brief TEXT,
        cover TEXT,
        local_cover_path TEXT,
        privacy TEXT DEFAULT 'private',
        created_at TEXT,
        updated_at TEXT,
        synced_at TEXT,
        is_dirty INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // 创建卡片表
    await db.execute('''
      CREATE TABLE cards (
        id INTEGER PRIMARY KEY,
        user_id TEXT,
        book_id INTEGER,
        type TEXT,
        type_version INTEGER,
        title TEXT,
        question TEXT,
        answer TEXT,
        extra TEXT,
        schedule_id INTEGER,
        created_at TEXT,
        updated_at TEXT,
        synced_at TEXT,
        is_dirty INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (book_id) REFERENCES books (id)
      )
    ''');

    // 创建卡片资源表
    await db.execute('''
      CREATE TABLE card_assets (
        id INTEGER PRIMARY KEY,
        card_id INTEGER,
        asset_id INTEGER,
        is_correct INTEGER DEFAULT 0,
        type TEXT,
        text TEXT,
        url TEXT,
        name TEXT,
        created_at TEXT,
        updated_at TEXT,
        synced_at TEXT,
        is_dirty INTEGER DEFAULT 0,
        FOREIGN KEY (card_id) REFERENCES cards (id)
      )
    ''');

    // 创建学习记录表
    await db.execute('''
      CREATE TABLE study_records (
        id INTEGER PRIMARY KEY,
        user_id TEXT,
        card_id INTEGER,
        rating INTEGER,
        elapsed_days INTEGER,
        scheduled_days INTEGER,
        review_time TEXT,
        state INTEGER,
        created_at TEXT,
        updated_at TEXT,
        synced_at TEXT,
        is_dirty INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (card_id) REFERENCES cards (id)
      )
    ''');

    // 创建设备表
    await db.execute('''
      CREATE TABLE devices (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        device_name TEXT,
        device_type TEXT,
        device_model TEXT,
        os_version TEXT,
        app_version TEXT,
        device_token TEXT,
        user_agent TEXT,
        ip_address TEXT,
        last_active TEXT,
        is_active INTEGER DEFAULT 1,
        login_count INTEGER DEFAULT 0,
        created_at TEXT,
        synced_at TEXT,
        is_dirty INTEGER DEFAULT 0,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )
    ''');

    // 创建同步记录表
    await db.execute('''
      CREATE TABLE sync_records (
        id TEXT PRIMARY KEY,
        user_id TEXT,
        device_id TEXT,
        table_name TEXT,
        record_id TEXT,
        action TEXT,
        status TEXT DEFAULT 'pending',
        client_version INTEGER DEFAULT 1,
        server_version INTEGER DEFAULT 0,
        data_before TEXT,
        data_after TEXT,
        sync_at TEXT,
        created_at TEXT,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (device_id) REFERENCES devices (id)
      )
    ''');

    // 创建索引
    await db.execute('CREATE INDEX idx_users_mobile ON users (mobile)');
    await db.execute('CREATE INDEX idx_books_user_id ON books (user_id)');
    await db.execute('CREATE INDEX idx_cards_book_id ON cards (book_id)');
    await db.execute('CREATE INDEX idx_cards_user_id ON cards (user_id)');
    await db.execute('CREATE INDEX idx_card_assets_card_id ON card_assets (card_id)');
    await db.execute('CREATE INDEX idx_study_records_user_id ON study_records (user_id)');
    await db.execute('CREATE INDEX idx_study_records_card_id ON study_records (card_id)');
    await db.execute('CREATE INDEX idx_devices_user_id ON devices (user_id)');
    await db.execute('CREATE INDEX idx_sync_records_user_id ON sync_records (user_id)');
    await db.execute('CREATE INDEX idx_sync_records_status ON sync_records (status)');
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // 数据库升级逻辑
    if (oldVersion < newVersion) {
      // 根据版本号执行相应的升级脚本
      // 例如：添加新字段、新表等
    }
  }

  // 通用查询方法
  Future<List<Map<String, dynamic>>> query(
    String table, {
    bool? distinct,
    List<String>? columns,
    String? where,
    List<Object?>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    return await _database.query(
      table,
      distinct: distinct,
      columns: columns,
      where: where,
      whereArgs: whereArgs,
      groupBy: groupBy,
      having: having,
      orderBy: orderBy,
      limit: limit,
      offset: offset,
    );
  }

  // 通用插入方法
  Future<int> insert(String table, Map<String, dynamic> values) async {
    return await _database.insert(table, values);
  }

  // 通用更新方法
  Future<int> update(
    String table,
    Map<String, dynamic> values, {
    String? where,
    List<Object?>? whereArgs,
  }) async {
    return await _database.update(
      table,
      values,
      where: where,
      whereArgs: whereArgs,
    );
  }

  // 通用删除方法
  Future<int> delete(
    String table, {
    String? where,
    List<Object?>? whereArgs,
  }) async {
    return await _database.delete(
      table,
      where: where,
      whereArgs: whereArgs,
    );
  }

  // 执行原生SQL
  Future<List<Map<String, dynamic>>> rawQuery(
    String sql, [
    List<Object?>? arguments,
  ]) async {
    return await _database.rawQuery(sql, arguments);
  }

  // 执行原生SQL（无返回值）
  Future<void> rawExecute(String sql, [List<Object?>? arguments]) async {
    await _database.execute(sql, arguments);
  }

  // 事务操作
  Future<T> transaction<T>(Future<T> Function(Transaction txn) action) async {
    return await _database.transaction(action);
  }

  // 批量操作
  Future<List<Object?>> batch(void Function(Batch batch) operations) async {
    final batch = _database.batch();
    operations(batch);
    return await batch.commit();
  }

  // 清空所有表数据（保留表结构）
  Future<void> clearAllData() async {
    await transaction((txn) async {
      await txn.execute('DELETE FROM sync_records');
      await txn.execute('DELETE FROM study_records');
      await txn.execute('DELETE FROM card_assets');
      await txn.execute('DELETE FROM cards');
      await txn.execute('DELETE FROM books');
      await txn.execute('DELETE FROM devices');
      await txn.execute('DELETE FROM user_configs');
      await txn.execute('DELETE FROM users');
    });
  }

  // 关闭数据库
  Future<void> close() async {
    await _database.close();
  }
}
