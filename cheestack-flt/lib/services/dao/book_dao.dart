import 'package:cheestack_flt/models/index.dart';
import 'package:cheestack_flt/shared/utils/index.dart';
import 'base_dao.dart';

/// 书籍数据访问对象
class BookDao extends BaseDao<BookModel> {
  @override
  String get tableName => 'books';
  
  @override
  String get primaryKey => 'id';
  
  @override
  BookModel fromMap(Map<String, dynamic> map) {
    return BookModel(
      id: map['id'],
      name: map['name'],
      brief: map['brief'],
      cover: map['cover'],
      localCoverPath: map['local_cover_path'],
      privacy: map['privacy'],
      createdAt: map['created_at'],
      updatedAt: map['updated_at'],
    );
  }
  
  @override
  Map<String, dynamic> toMap(BookModel entity) {
    return {
      'id': entity.id,
      'name': entity.name,
      'brief': entity.brief,
      'cover': entity.cover,
      'local_cover_path': entity.localCoverPath,
      'privacy': entity.privacy,
      'created_at': entity.createdAt,
      'updated_at': entity.updatedAt,
    };
  }
  
  /// 插入书籍（需要用户ID）
  Future<int> insertWithUserId(BookModel book, String userId) async {
    Console.log('BookDao.insertWithUserId 开始执行');
    Console.log('用户ID: $userId');
    Console.log('书籍数据: ${book.toJson()}');

    try {
      final map = toMap(book);
      map['user_id'] = userId;

      // 添加时间戳
      final now = DateTime.now().toIso8601String();
      map['created_at'] = now;
      map['updated_at'] = now;
      map['is_dirty'] = 1; // 标记为未同步

      Console.log('准备插入的数据: $map');

      final id = await db.insert(tableName, map);
      Console.log('数据库插入成功，返回ID: $id');

      // 添加同步记录
      Console.log('添加同步记录...');
      await addSyncRecord(
          'create', map['id']?.toString() ?? id.toString(), null, map);
      Console.log('同步记录添加完成');

      Console.log('BookDao.insertWithUserId 执行完成，返回ID: $id');
      return id;
    } catch (e, stackTrace) {
      Console.log('BookDao.insertWithUserId 发生错误: $e');
      Console.log('错误堆栈: $stackTrace');
      rethrow;
    }
  }
  
  /// 根据用户ID查找书籍
  Future<List<BookModel>> findByUserId(String userId, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    return await findWhere(
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: orderBy ?? 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 根据名称搜索书籍
  Future<List<BookModel>> searchByName(String userId, String keyword, {
    int? limit,
    int? offset,
  }) async {
    return await findWhere(
      where: 'user_id = ? AND name LIKE ?',
      whereArgs: [userId, '%$keyword%'],
      orderBy: 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 根据隐私设置查找书籍
  Future<List<BookModel>> findByPrivacy(String userId, String privacy, {
    String? orderBy,
    int? limit,
    int? offset,
  }) async {
    return await findWhere(
      where: 'user_id = ? AND privacy = ?',
      whereArgs: [userId, privacy],
      orderBy: orderBy ?? 'created_at DESC',
      limit: limit,
      offset: offset,
    );
  }
  
  /// 更新书籍信息
  Future<int> updateBook(int bookId, {
    String? name,
    String? brief,
    String? cover,
    String? localCoverPath,
    String? privacy,
  }) async {
    final updateData = <String, dynamic>{
      'updated_at': DateTime.now().toIso8601String(),
      'is_dirty': 1,
    };

    if (name != null) updateData['name'] = name;
    if (brief != null) updateData['brief'] = brief;
    if (cover != null) updateData['cover'] = cover;
    if (localCoverPath != null) updateData['local_cover_path'] = localCoverPath;
    if (privacy != null) updateData['privacy'] = privacy;

    return await db.update(
      tableName,
      updateData,
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }
  
  /// 更新书籍封面
  Future<int> updateCover(int bookId, String cover) async {
    return await db.update(
      tableName,
      {
        'cover': cover,
        'updated_at': DateTime.now().toIso8601String(),
        'is_dirty': 1,
      },
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }
  
  /// 更新书籍隐私设置
  Future<int> updatePrivacy(int bookId, String privacy) async {
    return await db.update(
      tableName,
      {
        'privacy': privacy,
        'updated_at': DateTime.now().toIso8601String(),
        'is_dirty': 1,
      },
      where: 'id = ?',
      whereArgs: [bookId],
    );
  }
  
  /// 获取用户书籍数量
  Future<int> countByUserId(String userId) async {
    return await count(
      where: 'user_id = ?',
      whereArgs: [userId],
    );
  }
  
  /// 获取用户公开书籍数量
  Future<int> countPublicByUserId(String userId) async {
    return await count(
      where: 'user_id = ? AND privacy = ?',
      whereArgs: [userId, 'public'],
    );
  }
  
  /// 获取用户私有书籍数量
  Future<int> countPrivateByUserId(String userId) async {
    return await count(
      where: 'user_id = ? AND privacy = ?',
      whereArgs: [userId, 'private'],
    );
  }
  
  /// 检查书籍名称是否存在
  Future<bool> isNameExists(String userId, String name, {int? excludeId}) async {
    String where = 'user_id = ? AND name = ?';
    List<Object?> whereArgs = [userId, name];
    
    if (excludeId != null) {
      where += ' AND id != ?';
      whereArgs.add(excludeId);
    }
    
    final count = await this.count(
      where: where,
      whereArgs: whereArgs,
    );
    
    return count > 0;
  }
  
  /// 获取最近创建的书籍
  Future<List<BookModel>> findRecentByUserId(String userId, {int limit = 10}) async {
    return await findWhere(
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'created_at DESC',
      limit: limit,
    );
  }
  
  /// 获取最近更新的书籍
  Future<List<BookModel>> findRecentlyUpdatedByUserId(String userId, {int limit = 10}) async {
    return await findWhere(
      where: 'user_id = ?',
      whereArgs: [userId],
      orderBy: 'updated_at DESC',
      limit: limit,
    );
  }
  
  /// 删除用户的所有书籍
  Future<int> deleteAllByUserId(String userId) async {
    return await db.delete(
      tableName,
      where: 'user_id = ?',
      whereArgs: [userId],
    );
  }
  
  /// 批量更新书籍隐私设置
  Future<int> batchUpdatePrivacy(String userId, List<int> bookIds, String privacy) async {
    if (bookIds.isEmpty) return 0;

    final placeholders = bookIds.map((_) => '?').join(',');

    return await db.rawUpdate('''
      UPDATE $tableName
      SET privacy = ?, updated_at = ?, is_dirty = 1
      WHERE user_id = ? AND id IN ($placeholders)
    ''', [privacy, DateTime.now().toIso8601String(), userId, ...bookIds]);
  }
}
