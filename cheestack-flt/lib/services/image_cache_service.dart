part of services;

/// 图片缓存管理服务
/// 提供本地优先的图片缓存机制，确保用户体验的流畅性
class ImageCacheService extends GetxService {
  static ImageCacheService get to => Get.find();
  
  // 缓存目录管理
  late Directory _cacheDir;
  final Map<String, String> _urlToPathCache = {};
  final int _maxCacheDays = 30; // 最大缓存天数
  final int _maxCacheSize = 100 * 1024 * 1024; // 最大缓存大小 100MB
  
  Future<ImageCacheService> init() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      _cacheDir = Directory('${appDir.path}/image_cache');
      if (!await _cacheDir.exists()) {
        await _cacheDir.create(recursive: true);
      }
      Console.log('图片缓存服务初始化完成: ${_cacheDir.path}');
      
      // 启动时清理过期缓存
      unawaited(_cleanExpiredCache());
      
      return this;
    } catch (e) {
      Console.log('图片缓存服务初始化失败: $e');
      rethrow;
    }
  }
  
  /// 获取图片路径（本地优先）
  /// [cosUrl] COS图片URL
  /// [forceRefresh] 是否强制刷新缓存
  Future<String?> getImagePath(String? cosUrl, {bool forceRefresh = false}) async {
    if (cosUrl == null || cosUrl.isEmpty) return null;
    
    try {
      // 1. 检查本地缓存
      if (!forceRefresh) {
        final localPath = await _getLocalPath(cosUrl);
        if (localPath != null && await File(localPath).exists()) {
          Console.log('使用本地缓存图片: $localPath');
          return localPath;
        }
      }
      
      // 2. 从COS下载并缓存
      Console.log('从COS下载图片: $cosUrl');
      return await _downloadAndCache(cosUrl);
    } catch (e) {
      Console.log('获取图片路径失败: $e');
      return cosUrl; // 回退到原始URL
    }
  }
  
  /// 预加载图片到本地缓存
  /// [cosUrl] COS图片URL
  Future<String?> preloadImage(String? cosUrl) async {
    if (cosUrl == null || cosUrl.isEmpty) return null;
    
    try {
      // 检查是否已缓存
      final localPath = await _getLocalPath(cosUrl);
      if (localPath != null && await File(localPath).exists()) {
        return localPath;
      }
      
      // 后台下载
      return await _downloadAndCache(cosUrl);
    } catch (e) {
      Console.log('预加载图片失败: $e');
      return null;
    }
  }
  
  /// 获取本地缓存路径
  Future<String?> _getLocalPath(String cosUrl) async {
    // 先检查内存缓存
    if (_urlToPathCache.containsKey(cosUrl)) {
      return _urlToPathCache[cosUrl];
    }
    
    // 生成本地文件路径
    final fileName = _generateFileName(cosUrl);
    final localPath = '${_cacheDir.path}/$fileName';
    
    if (await File(localPath).exists()) {
      _urlToPathCache[cosUrl] = localPath;
      return localPath;
    }
    
    return null;
  }
  
  /// 下载图片并缓存到本地
  Future<String?> _downloadAndCache(String cosUrl) async {
    try {
      final response = await Dio().get(
        cosUrl,
        options: Options(
          responseType: ResponseType.bytes,
          receiveTimeout: const Duration(seconds: 30),
          sendTimeout: const Duration(seconds: 30),
        ),
      );
      
      if (response.statusCode == 200 && response.data != null) {
        final fileName = _generateFileName(cosUrl);
        final localFile = File('${_cacheDir.path}/$fileName');
        await localFile.writeAsBytes(response.data);
        
        // 更新缓存映射
        _urlToPathCache[cosUrl] = localFile.path;
        
        Console.log('图片下载并缓存成功: ${localFile.path}');
        return localFile.path;
      } else {
        Console.log('图片下载失败，状态码: ${response.statusCode}');
        return cosUrl;
      }
    } catch (e) {
      Console.log('图片下载失败: $e');
      return cosUrl; // 回退到原始URL
    }
  }
  
  /// 生成本地文件名
  String _generateFileName(String cosUrl) {
    // 使用URL的MD5作为文件名，避免特殊字符问题
    final uri = Uri.parse(cosUrl);
    final pathSegments = uri.pathSegments;
    final originalName = pathSegments.isNotEmpty ? pathSegments.last : 'image';
    final extension = originalName.contains('.') ? originalName.split('.').last : 'jpg';
    
    // 使用URL的哈希值作为文件名
    final hash = cosUrl.hashCode.abs().toString();
    return '${hash}_${DateTime.now().millisecondsSinceEpoch}.$extension';
  }
  
  /// 清理过期缓存
  Future<void> _cleanExpiredCache() async {
    try {
      final now = DateTime.now();
      final files = await _cacheDir.list().toList();
      int deletedCount = 0;
      int totalSize = 0;
      
      // 计算总大小并删除过期文件
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
          
          final daysDiff = now.difference(stat.modified).inDays;
          if (daysDiff > _maxCacheDays) {
            await file.delete();
            deletedCount++;
            Console.log('删除过期缓存文件: ${file.path}');
          }
        }
      }
      
      Console.log('缓存清理完成，删除 $deletedCount 个过期文件，当前缓存大小: ${(totalSize / 1024 / 1024).toStringAsFixed(2)}MB');
      
      // 如果缓存大小超过限制，删除最旧的文件
      if (totalSize > _maxCacheSize) {
        await _cleanOldestFiles();
      }
    } catch (e) {
      Console.log('清理缓存失败: $e');
    }
  }
  
  /// 清理最旧的文件
  Future<void> _cleanOldestFiles() async {
    try {
      final files = await _cacheDir.list().toList();
      final fileStats = <MapEntry<File, FileStat>>[];
      
      // 收集文件信息
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          fileStats.add(MapEntry(file, stat));
        }
      }
      
      // 按修改时间排序（最旧的在前）
      fileStats.sort((a, b) => a.value.modified.compareTo(b.value.modified));
      
      // 删除最旧的文件直到缓存大小合理
      int currentSize = fileStats.fold(0, (sum, entry) => sum + entry.value.size);
      int deletedCount = 0;
      
      for (final entry in fileStats) {
        if (currentSize <= _maxCacheSize * 0.8) break; // 保留80%的空间
        
        await entry.key.delete();
        currentSize -= entry.value.size;
        deletedCount++;
        Console.log('删除旧缓存文件: ${entry.key.path}');
      }
      
      Console.log('清理旧文件完成，删除 $deletedCount 个文件');
    } catch (e) {
      Console.log('清理旧文件失败: $e');
    }
  }
  
  /// 手动清理所有缓存
  Future<void> clearAllCache() async {
    try {
      if (await _cacheDir.exists()) {
        await _cacheDir.delete(recursive: true);
        await _cacheDir.create(recursive: true);
      }
      _urlToPathCache.clear();
      Console.log('所有图片缓存已清理');
    } catch (e) {
      Console.log('清理所有缓存失败: $e');
    }
  }
  
  /// 获取缓存统计信息
  Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final files = await _cacheDir.list().toList();
      int fileCount = 0;
      int totalSize = 0;
      
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          fileCount++;
          totalSize += stat.size;
        }
      }
      
      return {
        'fileCount': fileCount,
        'totalSize': totalSize,
        'totalSizeMB': (totalSize / 1024 / 1024).toStringAsFixed(2),
        'cacheDir': _cacheDir.path,
      };
    } catch (e) {
      Console.log('获取缓存统计失败: $e');
      return {};
    }
  }
}
